<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:modelValue']);

const localValue = ref(props.modelValue);

watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue;
});

watch(localValue, (newValue) => {
  // Ensure value is at least 1
  if (newValue < 1) {
    localValue.value = 1;
    return;
  }
  emit('update:modelValue', newValue);
});

function increment() {
  localValue.value++;
}

function decrement() {
  if (localValue.value <= 1) return;
  localValue.value--;
}

function handleInput(event) {
  const value = parseInt(event.target.value) || 1;
  localValue.value = value;
}
</script>

<template>
  <div class="quantity-section">
    <label for="quantity" class="quantity-label">Aantal:</label>
    <div class="quantity-controls">
      <button
        type="button"
        class="quantity-btn"
        @click="decrement"
        :disabled="localValue <= 1"
        aria-label="Decrease quantity"
      >
        -
      </button>
      <input
        id="quantity"
        :value="localValue"
        @input="handleInput"
        type="number"
        min="1"
        class="quantity-input"
        aria-label="Quantity"
      />
      <button
        type="button"
        class="quantity-btn"
        @click="increment"
        aria-label="Increase quantity"
      >
        +
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.quantity-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quantity-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.quantity-btn {
  background-color: #f8f9fa;
  border: none;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  transition: background-color 0.2s;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background-color: #e9ecef;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:first-child {
    border-right: 1px solid #ced4da;
  }

  &:last-child {
    border-left: 1px solid #ced4da;
  }
}

.quantity-input {
  border: none;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  width: 60px;
  height: 36px;
  outline: none;
  background-color: white;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

@media (max-width: 768px) {
  .quantity-section {
    justify-content: center;
  }
}
</style>
